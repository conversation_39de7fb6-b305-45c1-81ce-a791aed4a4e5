package org.example;

import org.eclipse.milo.opcua.sdk.client.OpcUaClient;
import org.eclipse.milo.opcua.stack.core.types.builtin.DataValue;
import org.eclipse.milo.opcua.stack.core.types.builtin.NodeId;
import org.eclipse.milo.opcua.stack.core.types.builtin.Variant;
import org.eclipse.milo.opcua.stack.core.types.enumerated.TimestampsToReturn;
import org.eclipse.milo.opcua.stack.core.types.structured.ReadResponse;
import org.eclipse.milo.opcua.stack.core.types.structured.ReadValueId;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.eclipse.milo.opcua.stack.core.types.builtin.unsigned.Unsigned.uint;

public class SimpleOPCUAClient {

    private static final String PLC_ADDRESS = "opc.tcp://***************:4840";
    private static final int NAMESPACE_INDEX = 3;  // s=3 命名空间
    private static final String HTTP_API_URL = "http://*************:9999/mes-control/api/v2/grab/receivePLCData";
    //private static final String HTTP_API_URL = "http://**************:9999/mes-control/api/v2/grab/receivePLCData";


    private OpcUaClient client;
    private List<NodeId> variableNodes = new ArrayList<>();
    // 保存节点的完整路径，便于显示
    private Map<NodeId, String> nodePathMap = new HashMap<>();

    // 当前MES数据实体，用于保存最新读取的数据
    private MESData currentData = new MESData();

    // HTTP客户端和JSON处理器
    private CloseableHttpClient httpClient;
    private ObjectMapper objectMapper;

    // 预定义的节点路径列表
    private static final String[] PREDEFINED_NODE_PATHS = {
        "\"MES\".\"dmc\"",
        "\"MES\".\"start\"",
        "\"MES\".\"stop\"",
        "\"MES\".\"pause\"",
        "\"MES\".\"running\"",
        "\"MES\".\"process\"",
        "\"MES\".\"processinfo\"[0].\"idx\"",
        "\"MES\".\"processinfo\"[0].\"start\"",
        "\"MES\".\"processinfo\"[1].\"idx\"",
        "\"MES\".\"processinfo\"[1].\"start\"",
        "\"MES\".\"processinfo\"[2].\"idx\"",
        "\"MES\".\"processinfo\"[2].\"start\"",
        "\"MES\".\"processinfo\"[3].\"idx\"",
        "\"MES\".\"processinfo\"[3].\"start\"",
        "\"MES\".\"processinfo\"[4].\"idx\"",
        "\"MES\".\"processinfo\"[4].\"start\"",
        "\"MES\".\"processinfo\"[5].\"idx\"",
        "\"MES\".\"processinfo\"[5].\"start\"",
        "\"MES\".\"processinfo\"[6].\"idx\"",
        "\"MES\".\"processinfo\"[6].\"start\"",
        "\"MES\".\"processinfo\"[7].\"idx\"",
        "\"MES\".\"processinfo\"[7].\"start\"",
        "\"MES\".\"processinfo\"[8].\"idx\"",
        "\"MES\".\"processinfo\"[8].\"start\"",
        "\"MES\".\"processinfo\"[9].\"idx\"",
        "\"MES\".\"processinfo\"[9].\"start\"",
        "\"MES\".\"processinfo\"[10].\"idx\"",
        "\"MES\".\"processinfo\"[10].\"start\"",
        "\"MES\".\"processinfo\"[11].\"idx\"",
        "\"MES\".\"processinfo\"[11].\"start\"",
        "\"MES\".\"processinfo\"[12].\"idx\"",
        "\"MES\".\"processinfo\"[12].\"start\"",
        "\"MES\".\"processinfo\"[13].\"idx\"",
        "\"MES\".\"processinfo\"[13].\"start\"",
        "\"MES\".\"processinfo\"[14].\"idx\"",
        "\"MES\".\"processinfo\"[14].\"start\"",
        "\"MES\".\"processinfo\"[15].\"idx\"",
        "\"MES\".\"processinfo\"[15].\"start\"",
        "\"MES\".\"processinfo\"[16].\"idx\"",
        "\"MES\".\"processinfo\"[16].\"start\"",
        "\"MES\".\"processinfo\"[17].\"idx\"",
        "\"MES\".\"processinfo\"[17].\"start\"",
        "\"MES\".\"processinfo\"[18].\"idx\"",
        "\"MES\".\"processinfo\"[18].\"start\"",
        "\"MES\".\"processinfo\"[19].\"idx\"",
        "\"MES\".\"processinfo\"[19].\"start\"",
        "\"MES\".\"processinfo\"[20].\"idx\"",
        "\"MES\".\"processinfo\"[20].\"start\"",
        "\"MES\".\"processinfo\"[21].\"idx\"",
        "\"MES\".\"processinfo\"[21].\"start\"",
        "\"MES\".\"processinfo\"[22].\"idx\"",
        "\"MES\".\"processinfo\"[22].\"start\"",
        "\"MES\".\"processinfo\"[23].\"idx\"",
        "\"MES\".\"processinfo\"[23].\"start\"",
        "\"MES\".\"processinfo\"[24].\"idx\"",
        "\"MES\".\"processinfo\"[24].\"start\"",
        "\"MES\".\"processinfo\"[25].\"idx\"",
        "\"MES\".\"processinfo\"[25].\"start\"",
        "\"MES\".\"processinfo\"[26].\"idx\"",
        "\"MES\".\"processinfo\"[26].\"start\"",
        "\"MES\".\"processinfo\"[27].\"idx\"",
        "\"MES\".\"processinfo\"[27].\"start\"",
        "\"MES\".\"processinfo\"[28].\"idx\"",
        "\"MES\".\"processinfo\"[28].\"start\"",
        "\"MES\".\"processinfo\"[29].\"idx\"",
        "\"MES\".\"processinfo\"[29].\"start\"",
        "\"MES\".\"processinfo\"[30].\"idx\"",
        "\"MES\".\"processinfo\"[30].\"start\"",
        "\"MES\".\"processinfo\"[31].\"idx\"",
        "\"MES\".\"processinfo\"[31].\"start\"",
        "\"MES\".\"processinfo\"[32].\"idx\"",
        "\"MES\".\"processinfo\"[32].\"start\"",
        "\"MES\".\"processinfo\"[33].\"idx\"",
        "\"MES\".\"processinfo\"[33].\"start\"",
        "\"MES\".\"processinfo\"[34].\"idx\"",
        "\"MES\".\"processinfo\"[34].\"start\"",
        "\"MES\".\"processinfo\"[35].\"idx\"",
        "\"MES\".\"processinfo\"[35].\"start\"",
        "\"MES\".\"processinfo\"[36].\"idx\"",
        "\"MES\".\"processinfo\"[36].\"start\"",
        "\"MES\".\"processinfo\"[37].\"idx\"",
        "\"MES\".\"processinfo\"[37].\"start\"",
        "\"MES\".\"processinfo\"[38].\"idx\"",
        "\"MES\".\"processinfo\"[38].\"start\"",
        "\"MES\".\"processinfo\"[39].\"idx\"",
        "\"MES\".\"processinfo\"[39].\"start\"",
        "\"MES\".\"processinfo\"[40].\"idx\"",
        "\"MES\".\"processinfo\"[40].\"start\"",
        "\"MES\".\"processinfo\"[41].\"idx\"",
        "\"MES\".\"processinfo\"[41].\"start\"",
        "\"MES\".\"processinfo\"[42].\"idx\"",
        "\"MES\".\"processinfo\"[42].\"start\"",
        "\"MES\".\"processinfo\"[43].\"idx\"",
        "\"MES\".\"processinfo\"[43].\"start\"",
        "\"MES\".\"processinfo\"[44].\"idx\"",
        "\"MES\".\"processinfo\"[44].\"start\"",
        "\"MES\".\"robot1\".\"powered_on\"",
        "\"MES\".\"robot1\".\"enabled\"",
        "\"MES\".\"robot1\".\"paused\"",
        "\"MES\".\"robot1\".\"inpos\"",
        "\"MES\".\"robot1\".\"interp_state\"",
        "\"MES\".\"robot1\".\"task_state\"",
        "\"MES\".\"robot1\".\"current_tool_id\"",
        "\"MES\".\"robot1\".\"current_user_id\"",
        "\"MES\".\"robot1\".\"actual_position\".\"rx\"",
        "\"MES\".\"robot1\".\"actual_position\".\"ry\"",
        "\"MES\".\"robot1\".\"actual_position\".\"rz\"",
        "\"MES\".\"robot1\".\"actual_position\".\"x\"",
        "\"MES\".\"robot1\".\"actual_position\".\"y\"",
        "\"MES\".\"robot1\".\"actual_position\".\"z\"",
        "\"MES\".\"robot1\".\"position\".\"rx\"",
        "\"MES\".\"robot1\".\"position\".\"ry\"",
        "\"MES\".\"robot1\".\"position\".\"rz\"",
        "\"MES\".\"robot1\".\"position\".\"x\"",
        "\"MES\".\"robot1\".\"position\".\"y\"",
        "\"MES\".\"robot1\".\"position\".\"z\"",
        "\"MES\".\"robot1\".\"joint_actual_position\".\"joint1\"",
        "\"MES\".\"robot1\".\"joint_actual_position\".\"joint2\"",
        "\"MES\".\"robot1\".\"joint_actual_position\".\"joint3\"",
        "\"MES\".\"robot1\".\"joint_actual_position\".\"joint4\"",
        "\"MES\".\"robot1\".\"joint_actual_position\".\"joint5\"",
        "\"MES\".\"robot1\".\"joint_actual_position\".\"joint6\"",
        "\"MES\".\"robot1\".\"joint_position\".\"joint1\"",
        "\"MES\".\"robot1\".\"joint_position\".\"joint2\"",
        "\"MES\".\"robot1\".\"joint_position\".\"joint3\"",
        "\"MES\".\"robot1\".\"joint_position\".\"joint4\"",
        "\"MES\".\"robot1\".\"joint_position\".\"joint5\"",
        "\"MES\".\"robot1\".\"joint_position\".\"joint6\"",
        "\"MES\".\"robot2\".\"powered_on\"",
        "\"MES\".\"robot2\".\"enabled\"",
        "\"MES\".\"robot2\".\"paused\"",
        "\"MES\".\"robot2\".\"inpos\"",
        "\"MES\".\"robot2\".\"interp_state\"",
        "\"MES\".\"robot2\".\"task_state\"",
        "\"MES\".\"robot2\".\"current_tool_id\"",
        "\"MES\".\"robot2\".\"current_user_id\"",
        "\"MES\".\"robot2\".\"actual_position\".\"rx\"",
        "\"MES\".\"robot2\".\"actual_position\".\"ry\"",
        "\"MES\".\"robot2\".\"actual_position\".\"rz\"",
        "\"MES\".\"robot2\".\"actual_position\".\"x\"",
        "\"MES\".\"robot2\".\"actual_position\".\"y\"",
        "\"MES\".\"robot2\".\"actual_position\".\"z\"",
        "\"MES\".\"robot2\".\"position\".\"rx\"",
        "\"MES\".\"robot2\".\"position\".\"ry\"",
        "\"MES\".\"robot2\".\"position\".\"rz\"",
        "\"MES\".\"robot2\".\"position\".\"x\"",
        "\"MES\".\"robot2\".\"position\".\"y\"",
        "\"MES\".\"robot2\".\"position\".\"z\"",
        "\"MES\".\"robot2\".\"joint_actual_position\".\"joint1\"",
        "\"MES\".\"robot2\".\"joint_actual_position\".\"joint2\"",
        "\"MES\".\"robot2\".\"joint_actual_position\".\"joint3\"",
        "\"MES\".\"robot2\".\"joint_actual_position\".\"joint4\"",
        "\"MES\".\"robot2\".\"joint_actual_position\".\"joint5\"",
        "\"MES\".\"robot2\".\"joint_actual_position\".\"joint6\"",
        "\"MES\".\"robot2\".\"joint_position\".\"joint1\"",
        "\"MES\".\"robot2\".\"joint_position\".\"joint2\"",
        "\"MES\".\"robot2\".\"joint_position\".\"joint3\"",
        "\"MES\".\"robot2\".\"joint_position\".\"joint4\"",
        "\"MES\".\"robot2\".\"joint_position\".\"joint5\"",
        "\"MES\".\"robot2\".\"joint_position\".\"joint6\"",
        "\"MES\".\"robot3\".\"powered_on\"",
        "\"MES\".\"robot3\".\"enabled\"",
        "\"MES\".\"robot3\".\"paused\"",
        "\"MES\".\"robot3\".\"inpos\"",
        "\"MES\".\"robot3\".\"interp_state\"",
        "\"MES\".\"robot3\".\"task_state\"",
        "\"MES\".\"robot3\".\"current_tool_id\"",
        "\"MES\".\"robot3\".\"current_user_id\"",
        "\"MES\".\"robot3\".\"actual_position\".\"rx\"",
        "\"MES\".\"robot3\".\"actual_position\".\"ry\"",
        "\"MES\".\"robot3\".\"actual_position\".\"rz\"",
        "\"MES\".\"robot3\".\"actual_position\".\"x\"",
        "\"MES\".\"robot3\".\"actual_position\".\"y\"",
        "\"MES\".\"robot3\".\"actual_position\".\"z\"",
        "\"MES\".\"robot3\".\"position\".\"rx\"",
        "\"MES\".\"robot3\".\"position\".\"ry\"",
        "\"MES\".\"robot3\".\"position\".\"rz\"",
        "\"MES\".\"robot3\".\"position\".\"x\"",
        "\"MES\".\"robot3\".\"position\".\"y\"",
        "\"MES\".\"robot3\".\"position\".\"z\"",
        "\"MES\".\"robot3\".\"joint_actual_position\".\"joint1\"",
        "\"MES\".\"robot3\".\"joint_actual_position\".\"joint2\"",
        "\"MES\".\"robot3\".\"joint_actual_position\".\"joint3\"",
        "\"MES\".\"robot3\".\"joint_actual_position\".\"joint4\"",
        "\"MES\".\"robot3\".\"joint_actual_position\".\"joint5\"",
        "\"MES\".\"robot3\".\"joint_actual_position\".\"joint6\"",
        "\"MES\".\"robot3\".\"joint_position\".\"joint1\"",
        "\"MES\".\"robot3\".\"joint_position\".\"joint2\"",
        "\"MES\".\"robot3\".\"joint_position\".\"joint3\"",
        "\"MES\".\"robot3\".\"joint_position\".\"joint4\"",
        "\"MES\".\"robot3\".\"joint_position\".\"joint5\"",
        "\"MES\".\"robot3\".\"joint_position\".\"joint6\""
    };

    public static void main(String[] args) {
        SimpleOPCUAClient demo = new SimpleOPCUAClient();
        try {
            demo.run();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void run() throws Exception {
        // 初始化HTTP客户端和JSON处理器
        initHttpClient();

        // 创建并连接OPC UA客户端
        client = OpcUaClient.create(PLC_ADDRESS);

        System.out.println("正在连接到PLC: " + PLC_ADDRESS);
        client.connect().get();
        System.out.println("连接成功!");

        // 初始化预定义节点
        System.out.println("正在初始化预定义节点列表...");
        initPredefinedNodes();
        System.out.println("找到 " + variableNodes.size() + " 个变量节点");

        // 每500毫秒读取一次数据
        while (true) {
            try {
                // 读取并显示节点值
                readAndDisplayNodeValues();

                // 发送JSON数据到HTTP接口
                sendDataToHttpApi();

                // 输出一些重要的MES数据信息，用于验证数据是否正确存入实体
                printMESDataSummary();

                //System.out.println("\n等待50毫秒后再次读取...\n");
                //Thread.sleep(50); // 等待500毫秒
            } catch (Exception e) {
                System.err.println("读取数据时发生错误: " + e.getMessage());
                e.printStackTrace();
                Thread.sleep(1000); // 出错后等待1秒再尝试
            }
        }
    }

    /**
     * 初始化HTTP客户端和JSON处理器
     */
    private void initHttpClient() {
        httpClient = HttpClients.createDefault();
        objectMapper = new ObjectMapper();
        System.out.println("HTTP客户端初始化完成，API地址: " + HTTP_API_URL);
    }

    /**
     * 将MES数据发送到HTTP接口
     */
    private void sendDataToHttpApi() {
        try {
            // 将MES数据转换为JSON字符串
            String jsonData = objectMapper.writeValueAsString(currentData);

            // 创建HTTP POST请求
            HttpPost httpPost = new HttpPost(HTTP_API_URL);
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Accept", "application/json");

            // 设置请求体
            StringEntity entity = new StringEntity(jsonData, "UTF-8");
            httpPost.setEntity(entity);

            // 发送请求
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");

                if (statusCode >= 200 && statusCode < 300) {
                    System.out.println("✓ 数据发送成功 - HTTP " + statusCode);
                    // System.out.println("响应: " + responseBody);
                } else {
                    System.err.println("✗ 数据发送失败 - HTTP " + statusCode);
                    System.err.println("响应: " + responseBody);
                }
            }

        } catch (Exception e) {
            System.err.println("发送数据到HTTP接口时出错: " + e.getMessage());
            // e.printStackTrace(); // 不打印详细错误堆栈，避免日志过多
        }
    }

    /**
     * 关闭HTTP客户端资源
     */
    private void closeHttpClient() {
        if (httpClient != null) {
            try {
                httpClient.close();
            } catch (Exception e) {
                System.err.println("关闭HTTP客户端时出错: " + e.getMessage());
            }
        }
    }

    /**
     * 打印MES数据摘要，用于验证数据是否正确存入实体
     */
    private void printMESDataSummary() {
        System.out.println("\n=== MES数据摘要 ===");
        System.out.println("时间戳: " + currentData.getTimestamp());
        System.out.println("总节点数: " + currentData.getTotalNodes() + ", 成功读取: " + currentData.getSuccessReadNodes());

        System.out.println("\n基本信息:");
        System.out.println("DMC: " + currentData.getDmc());
        System.out.println("开始: " + currentData.isStart() + ", 停止: " + currentData.isStop());
        System.out.println("暂停: " + currentData.isPause() + ", 运行中: " + currentData.isRunning());
        System.out.println("工序: " + currentData.getProcess());

        System.out.println("\n机器人1状态:");
        MESData.Robot robot1 = currentData.getRobot1();
        System.out.println("电源: " + robot1.isPoweredOn() + ", 使能: " + robot1.isEnabled());
        System.out.println("暂停: " + robot1.isPaused() + ", 到位: " + robot1.isInpos());
        System.out.println("程序状态: " + robot1.getInterpState() + ", 任务状态: " + robot1.getTaskState());
        System.out.println("当前工具ID: " + robot1.getCurrentToolId() + ", 当前用户ID: " + robot1.getCurrentUserId());

        System.out.println("实际位置(actual_position):");
        MESData.Position r1ActPos = robot1.getActualPosition();
        System.out.printf("  X=%.2f, Y=%.2f, Z=%.2f\n", r1ActPos.getX(), r1ActPos.getY(), r1ActPos.getZ());
        System.out.printf("  RX=%.2f, RY=%.2f, RZ=%.2f\n", r1ActPos.getRx(), r1ActPos.getRy(), r1ActPos.getRz());

        System.out.println("目标位置(position):");
        MESData.Position r1Pos = robot1.getPosition();
        System.out.printf("  X=%.2f, Y=%.2f, Z=%.2f\n", r1Pos.getX(), r1Pos.getY(), r1Pos.getZ());
        System.out.printf("  RX=%.2f, RY=%.2f, RZ=%.2f\n", r1Pos.getRx(), r1Pos.getRy(), r1Pos.getRz());

        System.out.println("实际关节位置(joint_actual_position):");
        MESData.JointPosition r1ActJoint = robot1.getJointActualPosition();
        System.out.printf("  J1=%.2f, J2=%.2f, J3=%.2f, J4=%.2f, J5=%.2f, J6=%.2f\n",
                r1ActJoint.getJoint1(), r1ActJoint.getJoint2(), r1ActJoint.getJoint3(),
                r1ActJoint.getJoint4(), r1ActJoint.getJoint5(), r1ActJoint.getJoint6());

        System.out.println("目标关节位置(joint_position):");
        MESData.JointPosition r1Joint = robot1.getJointPosition();
        System.out.printf("  J1=%.2f, J2=%.2f, J3=%.2f, J4=%.2f, J5=%.2f, J6=%.2f\n",
                r1Joint.getJoint1(), r1Joint.getJoint2(), r1Joint.getJoint3(),
                r1Joint.getJoint4(), r1Joint.getJoint5(), r1Joint.getJoint6());

        System.out.println("\n机器人2状态:");
        MESData.Robot robot2 = currentData.getRobot2();
        System.out.println("电源: " + robot2.isPoweredOn() + ", 使能: " + robot2.isEnabled());
        System.out.println("暂停: " + robot2.isPaused() + ", 到位: " + robot2.isInpos());
        System.out.println("程序状态: " + robot2.getInterpState() + ", 任务状态: " + robot2.getTaskState());
        System.out.println("当前工具ID: " + robot2.getCurrentToolId() + ", 当前用户ID: " + robot2.getCurrentUserId());

        System.out.println("实际位置(actual_position):");
        MESData.Position r2ActPos = robot2.getActualPosition();
        System.out.printf("  X=%.2f, Y=%.2f, Z=%.2f\n", r2ActPos.getX(), r2ActPos.getY(), r2ActPos.getZ());
        System.out.printf("  RX=%.2f, RY=%.2f, RZ=%.2f\n", r2ActPos.getRx(), r2ActPos.getRy(), r2ActPos.getRz());

        System.out.println("目标位置(position):");
        MESData.Position r2Pos = robot2.getPosition();
        System.out.printf("  X=%.2f, Y=%.2f, Z=%.2f\n", r2Pos.getX(), r2Pos.getY(), r2Pos.getZ());
        System.out.printf("  RX=%.2f, RY=%.2f, RZ=%.2f\n", r2Pos.getRx(), r2Pos.getRy(), r2Pos.getRz());

        System.out.println("实际关节位置(joint_actual_position):");
        MESData.JointPosition r2ActJoint = robot2.getJointActualPosition();
        System.out.printf("  J1=%.2f, J2=%.2f, J3=%.2f, J4=%.2f, J5=%.2f, J6=%.2f\n",
                r2ActJoint.getJoint1(), r2ActJoint.getJoint2(), r2ActJoint.getJoint3(),
                r2ActJoint.getJoint4(), r2ActJoint.getJoint5(), r2ActJoint.getJoint6());

        System.out.println("目标关节位置(joint_position):");
        MESData.JointPosition r2Joint = robot2.getJointPosition();
        System.out.printf("  J1=%.2f, J2=%.2f, J3=%.2f, J4=%.2f, J5=%.2f, J6=%.2f\n",
                r2Joint.getJoint1(), r2Joint.getJoint2(), r2Joint.getJoint3(),
                r2Joint.getJoint4(), r2Joint.getJoint5(), r2Joint.getJoint6());

        //System.out.println("\n机器人3状态:");
        //MESData.Robot robot3 = currentData.getRobot3();
        //System.out.println("电源: " + robot3.isPoweredOn() + ", 使能: " + robot3.isEnabled());
        //System.out.println("暂停: " + robot3.isPaused() + ", 到位: " + robot3.isInpos());
        //System.out.println("程序状态: " + robot3.getInterpState() + ", 任务状态: " + robot3.getTaskState());
        //System.out.println("当前工具ID: " + robot3.getCurrentToolId() + ", 当前用户ID: " + robot3.getCurrentUserId());
        //
        //System.out.println("实际位置(actual_position):");
        //MESData.Position r3ActPos = robot3.getActualPosition();
        //System.out.printf("  X=%.2f, Y=%.2f, Z=%.2f\n", r3ActPos.getX(), r3ActPos.getY(), r3ActPos.getZ());
        //System.out.printf("  RX=%.2f, RY=%.2f, RZ=%.2f\n", r3ActPos.getRx(), r3ActPos.getRy(), r3ActPos.getRz());
        //
        //System.out.println("目标位置(position):");
        //MESData.Position r3Pos = robot3.getPosition();
        //System.out.printf("  X=%.2f, Y=%.2f, Z=%.2f\n", r3Pos.getX(), r3Pos.getY(), r3Pos.getZ());
        //System.out.printf("  RX=%.2f, RY=%.2f, RZ=%.2f\n", r3Pos.getRx(), r3Pos.getRy(), r3Pos.getRz());
        //
        //System.out.println("实际关节位置(joint_actual_position):");
        //MESData.JointPosition r3ActJoint = robot3.getJointActualPosition();
        //System.out.printf("  J1=%.2f, J2=%.2f, J3=%.2f, J4=%.2f, J5=%.2f, J6=%.2f\n",
        //        r3ActJoint.getJoint1(), r3ActJoint.getJoint2(), r3ActJoint.getJoint3(),
        //        r3ActJoint.getJoint4(), r3ActJoint.getJoint5(), r3ActJoint.getJoint6());
        //
        //System.out.println("目标关节位置(joint_position):");
        //MESData.JointPosition r3Joint = robot3.getJointPosition();
        //System.out.printf("  J1=%.2f, J2=%.2f, J3=%.2f, J4=%.2f, J5=%.2f, J6=%.2f\n",
        //        r3Joint.getJoint1(), r3Joint.getJoint2(), r3Joint.getJoint3(),
        //        r3Joint.getJoint4(), r3Joint.getJoint5(), r3Joint.getJoint6());

        //System.out.println("\n过程信息:");
        //MESData.ProcessInfo[] processInfos = currentData.getProcessInfos();
        //for (int i = 0; i < Math.min(50, processInfos.length); i++) {
        //    System.out.println("Process[" + i + "]: idx=" + processInfos[i].getIdx() + ", start=" + processInfos[i].isStart());
        //}
        //System.out.println("...(共" + processInfos.length + "个过程信息)");
    }

    private void initPredefinedNodes() {
        //System.out.println("添加 " + PREDEFINED_NODE_PATHS.length + " 个预定义节点到读取列表");
        int successCount = 0;

        for (String path : PREDEFINED_NODE_PATHS) {
            try {
                NodeId nodeId = new NodeId(NAMESPACE_INDEX, path);
                variableNodes.add(nodeId);
                nodePathMap.put(nodeId, path);
                successCount++;
            } catch (Exception e) {
                System.err.println("添加节点路径失败: " + path + " - " + e.getMessage());
            }
        }

        //System.out.println("成功添加了 " + successCount + " 个节点");
    }

    private void readAndDisplayNodeValues() throws Exception {
        if (variableNodes.isEmpty()) {
            System.out.println("没有找到任何变量节点可读取");
            return;
        }

        //System.out.println("=== 读取所有节点值 ===");
        //System.out.println("时间: " + new java.util.Date());
        //System.out.println("节点数量: " + variableNodes.size());
        //System.out.println("----------------------------");

        // 更新MES数据实体的时间戳和总节点数
        currentData.setTimestamp(new Date());
        currentData.setTotalNodes(variableNodes.size());

        // 创建读取请求列表
        List<ReadValueId> readValueIds = new ArrayList<>();
        for (NodeId nodeId : variableNodes) {
            readValueIds.add(new ReadValueId(nodeId, uint(13), null, null)); // Value属性(13)
        }

        try {
            // 批量读取所有节点的值
            ReadResponse response = client.read(0.0, TimestampsToReturn.Both, readValueIds).get();
            DataValue[] dataValues = response.getResults();

            // 显示结果
            int goodReadCount = 0;
            Map<String, Object> valueMap = new HashMap<>(); // 用于批量更新MES数据实体

            for (int i = 0; i < variableNodes.size() && i < dataValues.length; i++) {
                NodeId nodeId = variableNodes.get(i);
                DataValue dataValue = dataValues[i];

                String nodePath = nodePathMap.getOrDefault(nodeId, nodeId.getIdentifier().toString());
                Variant value = dataValue.getValue();
                String statusCode = dataValue.getStatusCode() != null ?
                        dataValue.getStatusCode().toString() : "OK";

                boolean isGood = dataValue.getStatusCode() != null && dataValue.getStatusCode().isGood();
                if (isGood) {
                    goodReadCount++;

                    // 将值添加到Map中，稍后批量更新MES数据实体
                    Object nodeValue = (value != null) ? value.getValue() : null;
                    valueMap.put(nodePath, nodeValue);
                }

                //System.out.printf("%-50s | %-10s | %s%n",
                //        nodePath,
                //        isGood ? "OK" : "ERROR",
                //        value != null ? value.getValue() : "null");
            }

            // 更新成功读取计数和数据实体
            currentData.setSuccessReadNodes(goodReadCount);
            currentData.updateFromMap(valueMap);

            //System.out.println("----------------------------");
            //System.out.println("成功读取了 " + goodReadCount + " / " + variableNodes.size() + " 个节点");

        } catch (Exception e) {
            System.err.println("批量读取失败，尝试逐个读取...");
            readNodesIndividually();
        }
    }

    private void readNodesIndividually() {
        //System.out.println("开始逐个读取节点...");
        int successCount = 0;
        int totalCount = 0;
        Map<String, Object> valueMap = new HashMap<>(); // 用于批量更新MES数据实体

        for (NodeId nodeId : variableNodes) {
            totalCount++;
            try {
                List<ReadValueId> singleRead = new ArrayList<>();
                singleRead.add(new ReadValueId(nodeId, uint(13), null, null)); // Value属性(13)

                ReadResponse response = client.read(0.0, TimestampsToReturn.Both, singleRead).get();
                DataValue[] dataValues = response.getResults();

                if (dataValues.length > 0) {
                    DataValue dataValue = dataValues[0];
                    String nodePath = nodePathMap.getOrDefault(nodeId, nodeId.getIdentifier().toString());
                    Variant value = dataValue.getValue();
                    boolean isGood = dataValue.getStatusCode() != null && dataValue.getStatusCode().isGood();

                    if (isGood) {
                        successCount++;

                        // 将值添加到Map中，稍后批量更新MES数据实体
                        Object nodeValue = (value != null) ? value.getValue() : null;
                        valueMap.put(nodePath, nodeValue);
                    }

                    //System.out.printf("%-50s | %-10s | %s%n",
                    //        nodePath,
                    //        isGood ? "OK" : "ERROR",
                    //        value != null ? value.getValue() : "null");
                }
            } catch (Exception e) {
                String nodePath = nodePathMap.getOrDefault(nodeId, nodeId.getIdentifier().toString());
                System.out.printf("%-50s | %-10s | %s%n",
                        nodePath,
                        "ERROR",
                        "读取失败: " + e.getMessage());
            }

            // 每20个节点显示一次进度
            if (totalCount % 20 == 0) {
                System.out.println("进度: 已读取 " + totalCount + "/" + variableNodes.size() + " 个节点");
            }
        }

        // 更新成功读取计数和数据实体
        currentData.setSuccessReadNodes(successCount);
        currentData.updateFromMap(valueMap);

        System.out.println("----------------------------");
        System.out.println("成功读取了 " + successCount + " / " + variableNodes.size() + " 个节点");
    }

    /**
     * 获取当前MES数据实体
     * 可供外部系统（如前端）获取最新数据
     */
    public MESData getCurrentData() {
        return currentData;
    }
}
