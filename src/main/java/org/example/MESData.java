package org.example;

import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * MES数据实体类，用于封装所有从PLC中读取的节点数据
 * 使用Lombok简化getter/setter代码
 */
@Data
@NoArgsConstructor
public class MESData {
    private Date timestamp; // 数据读取时间戳
    private int totalNodes; // 总节点数
    private int successReadNodes; // 成功读取的节点数

    // 基本变量
    private String dmc;
    private boolean start;
    private boolean stop;
    private boolean pause;
    private boolean running;
    private int process;

    // 机器人状态
    private Robot robot1 = new Robot();
    private Robot robot2 = new Robot();
    private Robot robot3 = new Robot();

    // 过程信息数组，保存45个过程信息
    private ProcessInfo[] processInfos = new ProcessInfo[45];

    // 其他可能未定义的节点数据
    private Map<String, Object> additionalData = new HashMap<>();

    // 自定义构造函数，保留原有初始化逻辑
    public MESData(boolean initialize) {
        this.timestamp = new Date();
        // 初始化所有processInfos元素
        for (int i = 0; i < processInfos.length; i++) {
            processInfos[i] = new ProcessInfo();
        }
    }

    // 获取指定路径的数据值
    public Object getValueByPath(String path) {
        if (path == null) return null;

        // 处理基本变量
        if (path.equals("\"MES\".\"dmc\"")) return dmc;
        if (path.equals("\"MES\".\"start\"")) return start;
        if (path.equals("\"MES\".\"stop\"")) return stop;
        if (path.equals("\"MES\".\"pause\"")) return pause;
        if (path.equals("\"MES\".\"running\"")) return running;
        if (path.equals("\"MES\".\"process\"")) return process;

        // 处理processinfo数组
        if (path.startsWith("\"MES\".\"processinfo\"[")) {
            try {
                int startIdx = path.indexOf('[') + 1;
                int endIdx = path.indexOf(']');
                int index = Integer.parseInt(path.substring(startIdx, endIdx));

                if (index >= 0 && index < processInfos.length) {
                    if (path.endsWith(".\"idx\"")) {
                        return processInfos[index].getIdx();
                    } else if (path.endsWith(".\"start\"")) {
                        return processInfos[index].isStart();
                    }
                }
            } catch (Exception e) {
                return null;
            }
        }

        // 处理robot1数据
        if (path.startsWith("\"MES\".\"robot1\".")) {
            return robot1.getValueByPath(path.substring("\"MES\".\"robot1\".".length()));
        }

        // 处理robot2数据
        if (path.startsWith("\"MES\".\"robot2\".")) {
            return robot2.getValueByPath(path.substring("\"MES\".\"robot2\".".length()));
        }

        // 处理robot3数据
        if (path.startsWith("\"MES\".\"robot3\".")) {
            return robot3.getValueByPath(path.substring("\"MES\".\"robot3\".".length()));
        }

        // 尝试从额外数据map中获取
        return additionalData.get(path);
    }

    // 设置指定路径的数据值
    public void setValueByPath(String path, Object value) {
        if (path == null) return;

        // 处理基本变量
        if (path.equals("\"MES\".\"dmc\"")) {
            if (value != null) this.dmc = value.toString();
            return;
        }
        if (path.equals("\"MES\".\"start\"")) {
            this.start = convertToBoolean(value);
            return;
        }
        if (path.equals("\"MES\".\"stop\"")) {
            this.stop = convertToBoolean(value);
            return;
        }
        if (path.equals("\"MES\".\"pause\"")) {
            this.pause = convertToBoolean(value);
            return;
        }
        if (path.equals("\"MES\".\"running\"")) {
            this.running = convertToBoolean(value);
            return;
        }
        if (path.equals("\"MES\".\"process\"")) {
            if (value instanceof Number) this.process = ((Number) value).intValue();
            else if (value != null) {
                try {
                    this.process = Integer.parseInt(value.toString());
                } catch (NumberFormatException e) {
                    // 忽略无法解析的值
                }
            }
            return;
        }

        // 处理processinfo数组
        if (path.startsWith("\"MES\".\"processinfo\"[")) {
            try {
                int startIdx = path.indexOf('[') + 1;
                int endIdx = path.indexOf(']');
                int index = Integer.parseInt(path.substring(startIdx, endIdx));

                if (index >= 0 && index < processInfos.length) {
                    if (path.endsWith(".\"idx\"")) {
                        if (value instanceof Number) {
                            processInfos[index].setIdx(((Number) value).intValue());
                        } else if (value != null) {
                            try {
                                processInfos[index].setIdx(Integer.parseInt(value.toString()));
                            } catch (NumberFormatException e) {
                                // 忽略无法解析的值
                            }
                        }
                        return;
                    } else if (path.endsWith(".\"start\"")) {
                        processInfos[index].setStart(convertToBoolean(value));
                        return;
                    }
                }
            } catch (Exception e) {
                // 忽略异常
            }
        }

        // 处理robot1数据
        if (path.startsWith("\"MES\".\"robot1\".")) {
            robot1.setValueByPath(path.substring("\"MES\".\"robot1\".".length()), value);
            return;
        }

        // 处理robot2数据
        if (path.startsWith("\"MES\".\"robot2\".")) {
            robot2.setValueByPath(path.substring("\"MES\".\"robot2\".".length()), value);
            return;
        }

        // 处理robot3数据
        if (path.startsWith("\"MES\".\"robot3\".")) {
            robot3.setValueByPath(path.substring("\"MES\".\"robot3\".".length()), value);
            return;
        }

        // 存入额外数据map中
        additionalData.put(path, value);
    }

    // 更新所有数据的方法 - 从Map中批量更新
    public void updateFromMap(Map<String, Object> dataMap) {
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            setValueByPath(entry.getKey(), entry.getValue());
        }
    }

    /**
     * 通用布尔值转换方法
     */
    private static boolean convertToBoolean(Object value) {
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue() != 0.0;
        }
        if (value != null) {
            String str = value.toString().trim();
            // 如果是数字字符串
            try {
                double num = Double.parseDouble(str);
                return num != 0.0;
            } catch (NumberFormatException e) {
                // 如果不是数字，则按字符串处理
                return "true".equalsIgnoreCase(str) || "1".equals(str);
            }
        }
        return false;
    }

    // 内部类 - 机器人数据
    @Data
    @NoArgsConstructor
    public static class Robot {
        private boolean poweredOn;
        private boolean enabled;
        private boolean paused;
        private boolean inpos;
        private int interpState;
        private int taskState;
        private int currentToolId;
        private int currentUserId;

        // 机器人位置
        private Position actualPosition = new Position();
        private Position position = new Position();

        // 关节位置
        private JointPosition jointActualPosition = new JointPosition();
        private JointPosition jointPosition = new JointPosition();

        // 根据路径获取值 - 保留业务逻辑方法
        public Object getValueByPath(String path) {
            if ("\"powered_on\"".equals(path)) return poweredOn;
            if ("\"enabled\"".equals(path)) return enabled;
            if ("\"paused\"".equals(path)) return paused;
            if ("\"inpos\"".equals(path)) return inpos;
            if ("\"interp_state\"".equals(path)) return interpState;
            if ("\"task_state\"".equals(path)) return taskState;
            if ("\"current_tool_id\"".equals(path)) return currentToolId;
            if ("\"current_user_id\"".equals(path)) return currentUserId;

            // 处理position数据
            if (path.startsWith("\"position\".")) {
                return position.getValueByPath(path.substring("\"position\".".length()));
            }

            // 处理actual_position数据
            if (path.startsWith("\"actual_position\".")) {
                return actualPosition.getValueByPath(path.substring("\"actual_position\".".length()));
            }

            // 处理joint_position数据
            if (path.startsWith("\"joint_position\".")) {
                return jointPosition.getValueByPath(path.substring("\"joint_position\".".length()));
            }

            // 处理joint_actual_position数据
            if (path.startsWith("\"joint_actual_position\".")) {
                return jointActualPosition.getValueByPath(path.substring("\"joint_actual_position\".".length()));
            }

            return null;
        }

        // 根据路径设置值 - 保留业务逻辑方法
        public void setValueByPath(String path, Object value) {
            if ("\"powered_on\"".equals(path)) {
                this.poweredOn = convertToBoolean(value);
                return;
            }
            if ("\"enabled\"".equals(path)) {
                this.enabled = convertToBoolean(value);
                return;
            }
            if ("\"paused\"".equals(path)) {
                this.paused = convertToBoolean(value);
                return;
            }
            if ("\"inpos\"".equals(path)) {
                this.inpos = convertToBoolean(value);
                return;
            }
            if ("\"interp_state\"".equals(path)) {
                if (value instanceof Number) this.interpState = ((Number) value).intValue();
                else if (value != null) {
                    try {
                        this.interpState = Integer.parseInt(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }
            if ("\"task_state\"".equals(path)) {
                if (value instanceof Number) this.taskState = ((Number) value).intValue();
                else if (value != null) {
                    try {
                        this.taskState = Integer.parseInt(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }
            if ("\"current_tool_id\"".equals(path)) {
                if (value instanceof Number) this.currentToolId = ((Number) value).intValue();
                else if (value != null) {
                    try {
                        this.currentToolId = Integer.parseInt(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }
            if ("\"current_user_id\"".equals(path)) {
                if (value instanceof Number) this.currentUserId = ((Number) value).intValue();
                else if (value != null) {
                    try {
                        this.currentUserId = Integer.parseInt(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }

            // 处理position数据
            if (path.startsWith("\"position\".")) {
                position.setValueByPath(path.substring("\"position\".".length()), value);
                return;
            }

            // 处理actual_position数据
            if (path.startsWith("\"actual_position\".")) {
                actualPosition.setValueByPath(path.substring("\"actual_position\".".length()), value);
                return;
            }

            // 处理joint_position数据
            if (path.startsWith("\"joint_position\".")) {
                jointPosition.setValueByPath(path.substring("\"joint_position\".".length()), value);
                return;
            }

            // 处理joint_actual_position数据
            if (path.startsWith("\"joint_actual_position\".")) {
                jointActualPosition.setValueByPath(path.substring("\"joint_actual_position\".".length()), value);
                return;
            }
        }
    }

    // 内部类 - 位置数据
    @Data
    @NoArgsConstructor
    public static class Position {
        private double rx;
        private double ry;
        private double rz;
        private double x;
        private double y;
        private double z;

        // 根据路径获取值 - 保留业务逻辑方法
        public Object getValueByPath(String path) {
            if ("\"rx\"".equals(path)) return rx;
            if ("\"ry\"".equals(path)) return ry;
            if ("\"rz\"".equals(path)) return rz;
            if ("\"x\"".equals(path)) return x;
            if ("\"y\"".equals(path)) return y;
            if ("\"z\"".equals(path)) return z;
            return null;
        }

        // 根据路径设置值 - 保留业务逻辑方法
        public void setValueByPath(String path, Object value) {
            if ("\"rx\"".equals(path)) {
                if (value instanceof Number) this.rx = ((Number) value).doubleValue();
                else if (value != null) {
                    try {
                        this.rx = Double.parseDouble(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }
            if ("\"ry\"".equals(path)) {
                if (value instanceof Number) this.ry = ((Number) value).doubleValue();
                else if (value != null) {
                    try {
                        this.ry = Double.parseDouble(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }
            if ("\"rz\"".equals(path)) {
                if (value instanceof Number) this.rz = ((Number) value).doubleValue();
                else if (value != null) {
                    try {
                        this.rz = Double.parseDouble(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }
            if ("\"x\"".equals(path)) {
                if (value instanceof Number) this.x = ((Number) value).doubleValue();
                else if (value != null) {
                    try {
                        this.x = Double.parseDouble(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }
            if ("\"y\"".equals(path)) {
                if (value instanceof Number) this.y = ((Number) value).doubleValue();
                else if (value != null) {
                    try {
                        this.y = Double.parseDouble(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }
            if ("\"z\"".equals(path)) {
                if (value instanceof Number) this.z = ((Number) value).doubleValue();
                else if (value != null) {
                    try {
                        this.z = Double.parseDouble(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }
        }
    }

    // 内部类 - 关节位置数据
    @Data
    @NoArgsConstructor
    public static class JointPosition {
        private double joint1;
        private double joint2;
        private double joint3;
        private double joint4;
        private double joint5;
        private double joint6;

        // 根据路径获取值 - 保留业务逻辑方法
        public Object getValueByPath(String path) {
            if ("\"joint1\"".equals(path)) return joint1;
            if ("\"joint2\"".equals(path)) return joint2;
            if ("\"joint3\"".equals(path)) return joint3;
            if ("\"joint4\"".equals(path)) return joint4;
            if ("\"joint5\"".equals(path)) return joint5;
            if ("\"joint6\"".equals(path)) return joint6;
            return null;
        }

        // 根据路径设置值 - 保留业务逻辑方法
        public void setValueByPath(String path, Object value) {
            if ("\"joint1\"".equals(path)) {
                if (value instanceof Number) this.joint1 = ((Number) value).doubleValue();
                else if (value != null) {
                    try {
                        this.joint1 = Double.parseDouble(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }
            if ("\"joint2\"".equals(path)) {
                if (value instanceof Number) this.joint2 = ((Number) value).doubleValue();
                else if (value != null) {
                    try {
                        this.joint2 = Double.parseDouble(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }
            if ("\"joint3\"".equals(path)) {
                if (value instanceof Number) this.joint3 = ((Number) value).doubleValue();
                else if (value != null) {
                    try {
                        this.joint3 = Double.parseDouble(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }
            if ("\"joint4\"".equals(path)) {
                if (value instanceof Number) this.joint4 = ((Number) value).doubleValue();
                else if (value != null) {
                    try {
                        this.joint4 = Double.parseDouble(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }
            if ("\"joint5\"".equals(path)) {
                if (value instanceof Number) this.joint5 = ((Number) value).doubleValue();
                else if (value != null) {
                    try {
                        this.joint5 = Double.parseDouble(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }
            if ("\"joint6\"".equals(path)) {
                if (value instanceof Number) this.joint6 = ((Number) value).doubleValue();
                else if (value != null) {
                    try {
                        this.joint6 = Double.parseDouble(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的值
                    }
                }
                return;
            }
        }
    }

    // 内部类 - 过程信息数据
    @Data
    @NoArgsConstructor
    public static class ProcessInfo {
        private int idx;
        private boolean start;
    }
}
